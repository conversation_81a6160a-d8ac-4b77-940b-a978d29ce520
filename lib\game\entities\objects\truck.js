ig.module('game.entities.objects.truck')
.requires(
    'plugins.utils.entity-extended',
    'game.entities.spawn-grid',
    'game.entities.path-head',
    'plugins.path-smoother',
    'plugins.utils.colors',
    'plugins.math.sat',
    'game.entities.objects.parking-slot'
)
.defines(function () {
    "use strict";

    EntityTruck = ig.global.EntityTruck = ig.EntityExtended.extend({
        name: 'truck-',
        zIndex: 10,
        type: ig.Entity.TYPE.A,
        checkAgainst: ig.Entity.TYPE.NONE,
        collides: ig.Entity.COLLIDES.NEVER,

        // --- General Movement ---
        speed: 50,
        rotationSpeed: 0.1, 
        pivotOffsetRatio: 0.05, // Percentage of the truck's height to offset the pivot point
        angleOffset: Math.PI / 2, // Visual rotation correction for the sprite
        
        // --- Spawning ---
        spawnPoint: null,
        isSpawning: true,

        // --- Parking ---
        parkingSpeed: 50,
        parkingRotationSpeed: 0.08,
        parkingThreshold: 5, // Distance to target to be considered "at" the parking spot
        parkingWaitTime: 3, // Seconds to wait after parking
        parkingWaitTimeList: [2, 3, 4, 5],
        parkingSlots: [],
        isParking: false,
        parkingTargetSlotInfo: null,
        isParked: false,
        parkingWaitTimer: 0,
        intendedParkingSlotName: null,

        // --- Collision ---
        buildingCollisionResponseSpeed: 0.02,
        minCollisionDuration: 0.2,
        isInBuildingCollisionResponse: false,
        buildingCollisionResponseProgress: 0,
        collisionCooldown: 0,
        needsCollisionRestore: false,
        _originalCollidesWith: null,
        attackBoxPercentage: 0.2,
        attackVertices: [],
        attackShape: null,
  
        // --- Input & Trajectory ---
        minimumDragDistance: 80, // Min pixels user must drag to start drawing a path
        optimizationRadius: 50, // Radius to search for a point to start the optimized parking path
        isClicked: false,
        isDragging: false,
        initialDragPos: { x: 0, y: 0 },
        currentDragPos: { x: 0, y: 0 },
        clickTime: 0,
        trajectory: [],
        trajectoryComplete: false,
        trajectorySnapped: false,
        minTrajectoryDistance: 0, // Minimum length of a valid user-drawn path
        isCurrentPathInvalid: false,
        pathHead: null,
        pathHeadRadius: 0,

        // --- State & Timers ---
        truckState: 'none', // 'none', 'parking', 'waiting', 'exit-ready', 'exiting', 'exited'
        currentWaypointStartTime: null,
        waypointTimeoutDuration: 3, // Max time to be stuck on a waypoint before doing something

        // --- Rendering & Visuals ---
        idleSheetInfo: {
            sheetImage: new ig.Image('media/graphics/sprites/vehicles/truck.png'),
            frameCountX: 8,
            frameCountY: 1
        },
        currentAnim: null,
        color: null,
        colorIndicatorRadius: 10,
        timerCircleSize: 50,
        timerCircleColor: 'rgba(0, 200, 255, 0.7)',
        timerCircleStroke: 'rgba(255, 255, 255, 0.8)',
        timerStrokeWidth: 3,
        circleColor: "#00FFFF",
        circleRadius: 8,
        circleAlpha: 0.8,
        dashLineAlpha: 0.8,
        solidLineAlpha: 0,
        alphaLerpSpeed: 0.05,

        // --- Physics & Geometry ---
        size: { x: 1, y: 1 }, // Will be set based on animation sheet
        halfW: 0,
        angle: 0,
        targetAngle: 0,
        direction: 0,
        pivotOffset: { x: 0, y: 0 },
        angleChangedThisFrame: false,
        finalDirectionX: 0,
        finalDirectionY: 0,
        finalAngleForMovement: null,
        vertices: [], 
        shape: null,  
        useCustomVertices: true,
        customVertices: {
            "truck1n": [{ x: -33.14, y: -94.56 }, { x: 30.02, y: -94.56 }, { x: 31.42, y: 98.77 }, { x: -31.74, y: 97.37 }],
            "truck1w": [{ x: -37.70, y: -98.77 }, { x: 36.69, y: -98.77 }, { x: 38.09, y: 101.57 }, { x: -39.10, y: 101.57 }],
            'truck2n': [{ x: -31.49, y: -92.69 }, { x: 27.70, y: -92.10 }, { x: 28.89, y: 94.47 }, { x: -32.09, y: 95.65 }],
            'truck2w': [{ x: -31.49, y: -92.69 }, { x: 27.70, y: -92.10 }, { x: 28.89, y: 94.47 }, { x: -32.09, y: 95.65 }],
            'truck3n': [{ x: -29.24, y: -93.28 }, { x: 24.61, y: -93.28 }, { x: 29.95, y: 97.43 }, { x: -30.43, y: 96.84 }],
            'truck3w': [{ x: -29.24, y: -93.28 }, { x: 24.61, y: -93.28 }, { x: 29.95, y: 97.43 }, { x: -30.43, y: 96.84 }],
            'truck4n': [{ x: -29.96, y: -109.88 }, { x: 26.27, y: -110.48 }, { x: 27.45, y: 112.26 }, { x: -29.96, y: 112.26 }],
            'truck4w': [{ x: -29.96, y: -109.88 }, { x: 26.27, y: -110.48 }, { x: 27.45, y: 112.26 }, { x: -29.96, y: 112.26 }]
        },
        
        // --- Internal ---
        truckType: null,
        truckTypes: ["truck1n", "truck2n", "truck3n", "truck4n"],
        // currentTarget: null,
        debugMode: false,
        satInstance: null,
        pathSmoother: null,
        currentWaypointIndex: 0,

        // --- Proximity Detection System ---
        proximityThreshold: 210, // Distance in pixels for proximity detection
        proximityCheckCooldown: 0.25 * 60, // Seconds between proximity checks for performance
        proximityCheckTimer: 0,
        nearbyTrucks: [], // Array of trucks currently in proximity
        isInProximityWarning: false, // Flag to track if truck is currently showing warning state
        proximityHonkCooldown: 1.0 * 60, // Seconds between honk sounds to avoid spam
        proximityHonkTimer: 0,
        originalTruckType: null, // Store original truck type for reverting from warning state

        // =================================================================================
        // SMOKE PARTICLE TRAIL SYSTEM
        // =================================================================================
        // Configurable smoke particle properties
        smokeConfig: {
            maxParticles: 15,           // Maximum number of particles
            emissionRate: 0.08 * 60,    // Frames between particle emissions (at 60 FPS: 0.08s * 60 = 4.8)
            minSpeedForSmoke: 10,       // Minimum speed to emit smoke (pixels/second)
            particleLifetime: 1.0 * 60, // How long particles live (frames at 60 FPS: 1.0s * 60 = 60)
            initialSize: 3,             // Starting particle size (pixels)
            maxSize: 24,                // Maximum particle size (pixels)
            maxAlpha: 0.4,              // Maximum particle transparency (0-1)
            velocitySpread: 30,         // Random velocity spread (pixels/second)
            dragFactor: 0.95,           // Velocity drag per frame (at 60 FPS)
            colors: {
                inner: [200, 200, 200], // Inner gradient color [R,G,B]
                middle: [150, 150, 150], // Middle gradient color [R,G,B]
                outer: [100, 100, 100]   // Outer gradient color [R,G,B]
            }
        },
        
        // Runtime properties
        smokeParticles: [],
        smokeEmitTimer: 0,
        
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.ctx = ig.system.context;

            this.initTruck();
            ig.game.sortEntitiesDeferred();
        },

        initTruck: function () {
            var randomIndex = Math.floor(Math.random() * this.truckTypes.length);
            this.truckType = this.truckTypes[randomIndex];

            this.initAnimations();
            this.onTruckTypeChanged(this.truckType);

            this.minTrajectoryDistance = 0;
            this.halfW = this.size.x / 2;
            this.satInstance = new ig.SAT();
            this.pathSmoother = new PathSmoother();
            this.updatePathHeadRadius();
            if (this.spawnPoint) {
                this.setupSpawnPositionAndDirection();
            }
            this.fetchParkingSlots();

            this.updatePivotOffset();
            this.updateRotatedVertices();

            this.parkingWaitTime = this.parkingWaitTimeList[randomIndex];

            this.name = 'truck-' + this.id;
        },

        initAnimations: function () {
            this.anims.truck1w = new ig.Animation(this.idleSheet, 1, [0], true);
            this.anims.truck1n = new ig.Animation(this.idleSheet, 1, [1], true);
            this.anims.truck2w = new ig.Animation(this.idleSheet, 1, [2], true);
            this.anims.truck2n = new ig.Animation(this.idleSheet, 1, [3], true);
            this.anims.truck3w = new ig.Animation(this.idleSheet, 1, [4], true);
            this.anims.truck3n = new ig.Animation(this.idleSheet, 1, [5], true);
            this.anims.truck4w = new ig.Animation(this.idleSheet, 1, [6], true);
            this.anims.truck4n = new ig.Animation(this.idleSheet, 1, [7], true);
        },

        onTruckTypeChanged: function (newTruckType) {
            if (this.anims[newTruckType]) this.currentAnim = this.anims[newTruckType];
            else this.currentAnim = this.anims.truck1n; 
            this.updatePathHeadRadius();
        },

        setTruckType: function (truckType) {
            if (!this.customVertices[truckType]) {
                return;
            }
            this.truckType = truckType;
            this.updateRotatedVertices();
            this.onTruckTypeChanged(truckType);
        },

        fetchParkingSlots: function () {
            this.parkingSlots = ig.currentCtrl.parkingSlots;
        },

        setupSpawnPositionAndDirection: function () {
            var spawnGrid = ig.game.getEntitiesByType(EntitySpawnGrid)[0];
            if (!spawnGrid) return;

            var spawnText = null;
            var spawnIndex = -1;

            // Defines the possible spawn areas and their corresponding direction and angle
            var pointsAndDirs = [
                { texts: spawnGrid.topText, textObjs: spawnGrid.topTextObj, dir: 1, angleRad: Math.PI / 2 },  // Top -> Down
                { texts: spawnGrid.botText, textObjs: spawnGrid.botTextObj, dir: 3, angleRad: -Math.PI / 2 }, // Bottom -> Up
                { texts: spawnGrid.leftText, textObjs: spawnGrid.leftTextObj, dir: 0, angleRad: 0 },          // Left -> Right
                { texts: spawnGrid.rightText, textObjs: spawnGrid.rightTextObj, dir: 2, angleRad: Math.PI }   // Right -> Left
            ];

            for (var i = 0; i < pointsAndDirs.length; i++) {
                spawnIndex = pointsAndDirs[i].texts.indexOf(this.spawnPoint);
                if (spawnIndex !== -1) {
                    spawnText = pointsAndDirs[i].textObjs[spawnIndex];
                    this.direction = pointsAndDirs[i].dir;
                    this.targetAngle = pointsAndDirs[i].angleRad;
                    this.angle = this.targetAngle;
                    break;
                }
            }

            if (spawnText) {
                var padding = 100;
                // Position the truck off-screen based on its spawn direction
                switch (this.direction) {
                    case 0: // Left
                        this.pos.x = -this.size.x - padding;
                        this.pos.y = spawnText.pos.y - this.size.y / 2;
                        break;
                    case 1: // Top
                        this.pos.x = spawnText.pos.x - this.size.x / 2;
                        this.pos.y = -this.size.y - padding;
                        break;
                    case 2: // Right
                        this.pos.x = ig.system.width + padding;
                        this.pos.y = spawnText.pos.y - this.size.y / 2;
                        break;
                    case 3: // Bottom
                        this.pos.x = spawnText.pos.x - this.size.x / 2;
                        this.pos.y = ig.system.height + padding;
                        break;
                }
            }
        },

        // =================================================================================
        // #region UPDATE LOOP
        // =================================================================================
        update: function () {
            if (ig.game.isPaused) return;
            this.parent();
            if (ig.currentCtrl && ig.currentCtrl.isGameOver) return;
            
            this.handleInput();
            this.updateMovementState();
            this.updateCollisions();
            this.updateParkingState();
            this.updateProximityDetection();

            this.updatePositionAndRotation();
            this.updateSmokeEffect();
            
            if (this.currentAnim) this.currentAnim.angle = this.angle + this.angleOffset;
        },

        updateMovementState: function () {
            if (this.truckState === 'exited') {
                this.checkOffScreen(true);
            } else {
                if (this.isRespondingToCollision()) {
                    this.updateBuildingCollisionResponse();
                    return;
                }
    
                if (this.isParkingEngaged()) {
                    this.updateParking();
                    return;
                }
    
                if (this.truckState === 'exit-ready' && this.trajectory.length > 0 && !this.isClicked) {
                    this.vacateSlot();
                }
            }

            if (this.isSpawning) {
                this.updateSpawning();
            }

            if (this.isFollowingTrajectory()) {
                this.updateTrajectoryMovement();
            } else if (this.hasCompletedTrajectoryButNotMovedOn()) {
                // After trajectory is done, smoothly align to final angle, slowing down if not aligned.
                var angleDiff = Math.abs(this.getShortestRotation(this.angle, this.targetAngle));
                // Slow down significantly if the angle is very different.
                var speedModifier = Math.pow(1 - Math.min(1, angleDiff / Math.PI), 2);
                this.moveInFacingDirection(this.speed * speedModifier);
                if (this.truckState !== 'exited') this.checkOffScreen(false);
            }

            this.updateAngleSimple();
        },

        updatePositionAndRotation: function () {
            // Only update position and vertices if there's movement or rotation
            if (this.vel.x !== 0 || this.vel.y !== 0 || this.angleChangedThisFrame) {
                this.updatePivotOffset();
                this.updateRotatedVertices();

                this.pos.x += this.vel.x * ig.system.tick;
                this.pos.y += this.vel.y * ig.system.tick;
            }
        },

        updateCollisions: function () {
            this.updateCollisionResponseState();
        },

        updateParkingState: function () {
            if (this.isParkingEngaged()) {
                this.updateParking();
            }
        },
        // #endregion
        // =================================================================================
        // #region DRAWING & RENDERING
        // =================================================================================
        draw: function () {
            this.drawSmokeEffect();
            this.drawMovementVisuals();
            this.parent();
            this.drawParkingTimer();
            this.drawColorIndicator();
            this.drawDebugInfo();
        },

        drawMovementVisuals: function () {
            this.drawMinDistanceIndicator();
            this.drawTrajectoryAndPathHead();
            this.drawSnapVisuals();
        },

        drawMinDistanceIndicator: function () {
            if (!this.isClicked || this.trajectory.length > 0 ||
                (this.isParked && this.truckState === 'exit-ready')) return;

            var ctx = ig.system.context;
            var pivotPoint = this.getPivotPoint();
            var mousePos = ig.game.io.mouse.getPos();
            var distToMouse = ig.utils.distanceBetween(pivotPoint.x, pivotPoint.y, mousePos.x, mousePos.y);

            var showAsInvalid = this.isCurrentPathInvalid || (distToMouse < this.minTrajectoryDistance);

            ctx.save();
            ctx.beginPath();
            ctx.arc(pivotPoint.x, pivotPoint.y, this.minTrajectoryDistance, 0, Math.PI * 2);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.lineWidth = 2;
            if (ctx.setLineDash) ctx.setLineDash([5, 5]);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(pivotPoint.x, pivotPoint.y);
            ctx.lineTo(mousePos.x, mousePos.y);
            ctx.strokeStyle = showAsInvalid ? 'rgba(255, 100, 100, 0.5)' : 'rgba(100, 255, 100, 0.5)';
            if (ctx.setLineDash) ctx.setLineDash([]);
            ctx.lineWidth = 2;
            ctx.stroke();
            ctx.restore();
        },

        drawTrajectoryAndPathHead: function () {
            var ctx = ig.system.context;

            if (this.isClicked && this.pathHead) {
                ctx.save();
                ctx.beginPath();
                ctx.arc(this.pathHead.pos.x, this.pathHead.pos.y, this.pathHead.getRadius(), 0, Math.PI * 2);
                var pathHeadColor = this.isCurrentPathInvalid ? 'rgba(255, 0, 0, 0.2)' : 'rgba(0, 255, 0, 0.2)';
                var pathHeadStroke = this.isCurrentPathInvalid ? 'rgba(255, 0, 0, 0.5)' : 'rgba(0, 255, 0, 0.5)';
                ctx.fillStyle = pathHeadColor;
                ctx.fill();
                ctx.strokeStyle = pathHeadStroke;
                ctx.lineWidth = 2;
                ctx.stroke();
                ctx.restore();
            }

            if (this.trajectory.length === 0) return;

            // Lerp alpha for smooth transition between dashed (drawing) and solid (set) lines
            if (this.isClicked) {
                this.dashLineAlpha = ig.utils.lerp(this.dashLineAlpha, 0.8, this.alphaLerpSpeed);
                this.solidLineAlpha = ig.utils.lerp(this.solidLineAlpha, 0, this.alphaLerpSpeed);
            } else {
                this.dashLineAlpha = ig.utils.lerp(this.dashLineAlpha, 0, this.alphaLerpSpeed);
                if (!this.trajectoryComplete || this.trajectory.length > 0) {
                     this.solidLineAlpha = ig.utils.lerp(this.solidLineAlpha, 0.8, this.alphaLerpSpeed);
                } else {
                     this.solidLineAlpha = ig.utils.lerp(this.solidLineAlpha, 0, this.alphaLerpSpeed);
                }
            }
            
            var startPoint = this.getPivotPoint();

            // Draw solid line for confirmed path
            if (this.solidLineAlpha > 0.01) {
                ctx.save();
                ctx.strokeStyle = 'rgba(255, 255, 255, ' + this.solidLineAlpha + ')';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(startPoint.x, startPoint.y);
                for (var i = 0; i < this.trajectory.length; i++) {
                    ctx.lineTo(this.trajectory[i].x, this.trajectory[i].y);
                }
                ctx.stroke();
                ctx.restore();
            }

            // Draw dashed line while user is drawing
            if (this.dashLineAlpha > 0.01) {
                ctx.save();
                if (ctx.setLineDash) ctx.setLineDash([8, 4]);
                ctx.strokeStyle = (this.isCurrentPathInvalid && this.isClicked) ?
                                    'rgba(255, 0, 0, ' + this.dashLineAlpha + ')' :
                                    'rgba(255, 255, 255, ' + this.dashLineAlpha + ')';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(startPoint.x, startPoint.y);
                for (var i = 0; i < this.trajectory.length; i++) {
                    ctx.lineTo(this.trajectory[i].x, this.trajectory[i].y);
                }
                ctx.stroke();
                if (ctx.setLineDash) ctx.setLineDash([]);
                ctx.restore();
            }

            // Draw circle at the end of the path
            if (this.trajectory.length > 0 && !this.trajectorySnapped) {
                var lastPoint = this.trajectory[this.trajectory.length - 1];
                ctx.save();
                ctx.beginPath();
                ctx.arc(lastPoint.x, lastPoint.y, this.circleRadius, 0, Math.PI * 2);
                ctx.fillStyle = this.circleColor;
                var currentAlpha = this.isClicked ? this.circleAlpha : Math.max(this.solidLineAlpha, this.dashLineAlpha);
                ctx.globalAlpha = currentAlpha;
                ctx.fill();
                ctx.restore();
            } else if (this.trajectorySnapped) {
                var lastPoint = this.trajectory[this.trajectory.length - 1];
                ctx.save();
                ctx.beginPath();
                ctx.arc(lastPoint.x, lastPoint.y, this.circleRadius * 0.5, 0, Math.PI * 2);
                ctx.fillStyle = this.circleColor;
                ctx.globalAlpha = 1;
                ctx.fill();
                ctx.restore();
            }
        },

        drawSnapVisuals: function () {
            if (!this.isClicked) return; // Only show when drawing

            var ctx = ig.system.context;
            var snapRadius = this.optimizationRadius || 150;

            for (var i = 0; i < this.parkingSlots.length; i++) {
                var slot = this.parkingSlots[i];
                var slotEntity = slot.slotEntity;

                // Only show visuals for valid, available slots of the correct color
                if (!slotEntity || slotEntity.isCurrentlyOccupied() || slot.color !== this.color) {
                    continue;
                }

                var frontPoint = slotEntity.getFrontPoint ? slotEntity.getFrontPoint() : null;
                if (!frontPoint) continue;

                ctx.save();
                ctx.beginPath();
                ctx.arc(
                    frontPoint.x - ig.game.screen.x,
                    frontPoint.y - ig.game.screen.y,
                    snapRadius,
                    0,
                    Math.PI * 2
                );
                ctx.strokeStyle = 'rgba(255, 255, 0, 0.4)'; // Yellow, semi-transparent
                ctx.lineWidth = 2;
                if (ctx.setLineDash) ctx.setLineDash([5, 8]);
                ctx.stroke();
                ctx.restore();
            }
        },

        drawColorIndicator: function () {
            if (!this.color || !this.ctx) return;
            var truckCenterX = this.pos.x + this.size.x / 2 - ig.game.screen.x;
            var truckCenterY = this.pos.y + this.size.y / 2 - ig.game.screen.y;
            var fillColor = 'rgba(128, 128, 128, 0.8)';
            if (typeof GameColors !== 'undefined') {
                switch (this.color) {
                    case GameColors.RED:    fillColor = 'rgba(255, 0, 0, 0.8)'; break;
                    case GameColors.BLUE:   fillColor = 'rgba(0, 0, 255, 0.8)'; break;
                    case GameColors.GREEN:  fillColor = 'rgba(0, 255, 0, 0.8)'; break;
                    case GameColors.YELLOW: fillColor = 'rgba(255, 255, 0, 0.8)'; break;
                    case GameColors.PURPLE: fillColor = 'rgba(128, 0, 128, 0.8)'; break;
                    case GameColors.ORANGE: fillColor = 'rgba(255, 165, 0, 0.8)'; break;
                }
            }

            this.ctx.save();
            this.ctx.fillStyle = fillColor;
            this.ctx.beginPath();
            this.ctx.arc(truckCenterX, truckCenterY, this.colorIndicatorRadius, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
            this.ctx.lineWidth = 2;
            this.ctx.stroke();
            this.ctx.restore();
        },

        drawParkingTimer: function () {
            if (!this.isParked || this.truckState !== 'waiting' || !this.parkingTargetSlotInfo) return;
            var progress = Math.min(1, Math.max(0, this.parkingWaitTimer / this.parkingWaitTime));
            var ctx = ig.system.context;
            var centerX = this.parkingTargetSlotInfo.center.x - ig.game.screen.x;
            var centerY = this.parkingTargetSlotInfo.center.y - ig.game.screen.y;
            var radius = this.timerCircleSize / 2;

            ctx.save();
            // Background circle
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
            ctx.fill();
            // Progress pie
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, -Math.PI / 2, -Math.PI / 2 + (Math.PI * 2 * progress), false);
            ctx.lineTo(centerX, centerY);
            ctx.fillStyle = this.timerCircleColor;
            ctx.fill();
            // Stroke
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.lineWidth = this.timerStrokeWidth;
            ctx.strokeStyle = this.timerCircleStroke;
            ctx.stroke();
            ctx.restore();
        },

        drawDebugInfo: function () {
            if (!ig.game.debugMode || !this.ctx) return;
            
            var truckCenterX = this.pos.x + this.size.x / 2 - ig.game.screen.x;
            var truckCenterY = this.pos.y + this.size.y / 2 - ig.game.screen.y;
            var pivotPoint = this.getPivotPoint();
            var pivotX = pivotPoint.x - ig.game.screen.x;
            var pivotY = pivotPoint.y - ig.game.screen.y;
            
            this.ctx.save();
            
            // Current angle (Green)
            this.ctx.strokeStyle = 'rgba(0, 255, 0, 0.8)';
            this.ctx.lineWidth = 3;
            this.ctx.beginPath();
            this.ctx.moveTo(truckCenterX, truckCenterY);
            var currentEndX = truckCenterX + Math.cos(this.angle) * 40;
            var currentEndY = truckCenterY + Math.sin(this.angle) * 40;
            this.ctx.lineTo(currentEndX, currentEndY);
            this.ctx.stroke();
            
            // Target angle (Yellow)
            this.ctx.strokeStyle = 'rgba(255, 255, 0, 0.8)';
            this.ctx.lineWidth = 2;
            this.ctx.beginPath();
            this.ctx.moveTo(truckCenterX, truckCenterY);
            var targetEndX = truckCenterX + Math.cos(this.targetAngle) * 35;
            var targetEndY = truckCenterY + Math.sin(this.targetAngle) * 35;
            this.ctx.lineTo(targetEndX, targetEndY);
            this.ctx.stroke();
            
            // Pivot point (Red)
            this.ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';
            this.ctx.beginPath();
            this.ctx.arc(pivotX, pivotY, 4, 0, Math.PI * 2);
            this.ctx.fill();
            
            // Line to current target (Cyan)
            // if (this.currentTarget) {
            //     this.ctx.strokeStyle = 'rgba(0, 255, 255, 0.6)';
            //     this.ctx.lineWidth = 2;
            //     this.ctx.beginPath();
            //     this.ctx.moveTo(pivotX, pivotY);
            //     this.ctx.lineTo(
            //         this.currentTarget.x - ig.game.screen.x,
            //         this.currentTarget.y - ig.game.screen.y
            //     );
            //     this.ctx.stroke();
            // }
            
            // Text info
            this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            this.ctx.font = '12px Arial';
            var speed = Math.sqrt(this.vel.x * this.vel.x + this.vel.y * this.vel.y).toFixed(1);
            var nearestDistance = this.getNearestTruckDistance();

            this.ctx.fillText('Name: ' + this.name, truckCenterX - 30, truckCenterY - 60);
            this.ctx.fillText('Speed: ' + speed, truckCenterX - 30, truckCenterY - 45);
            this.ctx.fillText('Nearest: ' + nearestDistance + 'px', truckCenterX - 30, truckCenterY - 30);
            
            this.ctx.restore();

            // Attack box (Blue)
            if (this.attackVertices.length > 0) {
                this.ctx.save();
                this.ctx.strokeStyle = 'rgba(0, 0, 255, 0.8)';
                this.ctx.lineWidth = 2;
                this.ctx.beginPath();
                var startPoint = this.attackVertices[this.attackVertices.length - 1];
                this.ctx.moveTo(startPoint.x - ig.game.screen.x, startPoint.y - ig.game.screen.y);
                for (var i = 0; i < this.attackVertices.length; i++) {
                    var p = this.attackVertices[i];
                    this.ctx.lineTo(p.x - ig.game.screen.x, p.y - ig.game.screen.y);
                }
                this.ctx.closePath();
                this.ctx.stroke();
                this.ctx.restore();
            }
        },
        // #endregion
        // =================================================================================
        // #region SMOKE PARTICLE TRAIL FUNCTIONS
        // =================================================================================
        setupSmokeEffect: function () {
            this.smokeParticles = [];
            this.smokeEmitTimer = 0;
        },

        updateSmokeEffect: function () {
            // Update existing particles
            this.updateSmokeParticles();
            
            // Check if truck is moving fast enough to emit smoke
            var currentSpeed = Math.sqrt(this.vel.x * this.vel.x + this.vel.y * this.vel.y);
            if (currentSpeed > this.smokeConfig.minSpeedForSmoke && !this.isParkingEngaged()) {
                // Update emit timer (frame-rate independent)
                this.smokeEmitTimer += ig.system.tick * 60;
                
                // Emit new particle if timer exceeded
                if (this.smokeEmitTimer >= this.smokeConfig.emissionRate) {
                    this.emitSmokeParticle();
                    this.smokeEmitTimer = 0;
                }
            }
        },

        emitSmokeParticle: function () {
            // Remove oldest particle if at max capacity
            if (this.smokeParticles.length >= this.smokeConfig.maxParticles) {
                this.smokeParticles.shift();
            }

            // Calculate exhaust position at the rear of the truck
            var centerX = this.pos.x + this.size.x / 2;
            var centerY = this.pos.y + this.size.y / 2;
            
            // Position smoke at the rear of the truck (opposite to facing direction)
            var exhaustDistance = this.size.y * 0.4; // Distance from center to rear
            var exhaustX = centerX - Math.cos(this.angle) * exhaustDistance;
            var exhaustY = centerY - Math.sin(this.angle) * exhaustDistance;
            
            // Add some random offset for natural dispersion
            var randomOffset = 15;
            exhaustX += (Math.random() - 0.5) * randomOffset;
            exhaustY += (Math.random() - 0.5) * randomOffset;

            // Create particle with initial velocity opposite to truck direction
            var baseVelocity = 20 + Math.random() * 10;
            var particle = {
                pos: { x: exhaustX, y: exhaustY },
                vel: {
                    x: -Math.cos(this.angle) * baseVelocity + (Math.random() - 0.5) * this.smokeConfig.velocitySpread,
                    y: -Math.sin(this.angle) * baseVelocity + (Math.random() - 0.5) * this.smokeConfig.velocitySpread
                },
                lifetime: this.smokeConfig.particleLifetime,
                maxLifetime: this.smokeConfig.particleLifetime,
                size: this.smokeConfig.initialSize + Math.random() * 4,
                maxSize: this.smokeConfig.maxSize + Math.random() * 8
            };
            
            this.smokeParticles.push(particle);
        },

        updateSmokeParticles: function () {
            for (var i = this.smokeParticles.length - 1; i >= 0; i--) {
                var particle = this.smokeParticles[i];
                
                // Update position (frame-rate independent)
                var deltaTime = ig.system.tick * 60;
                particle.pos.x += particle.vel.x * ig.system.tick;
                particle.pos.y += particle.vel.y * ig.system.tick;
                
                // Apply drag to velocity (frame-rate independent)
                var dragPerFrame = Math.pow(this.smokeConfig.dragFactor, deltaTime / 60);
                particle.vel.x *= dragPerFrame;
                particle.vel.y *= dragPerFrame;
                
                // Update lifetime (frame-rate independent)
                particle.lifetime -= ig.system.tick * 60;
                
                // Grow particle size over time
                var lifeProgress = 1 - (particle.lifetime / particle.maxLifetime);
                particle.size = particle.size + (particle.maxSize - particle.size) * lifeProgress * 0.1;
                
                // Remove expired particles
                if (particle.lifetime <= 0) {
                    this.smokeParticles.splice(i, 1);
                }
            }
        },

        drawSmokeEffect: function () {
            if (this.smokeParticles.length === 0) return;
            
            this.ctx.save();
            
            for (var i = 0; i < this.smokeParticles.length; i++) {
                var particle = this.smokeParticles[i];
                
                // Calculate alpha based on remaining lifetime
                var lifeProgress = particle.lifetime / particle.maxLifetime;
                var alpha = lifeProgress * this.smokeConfig.maxAlpha;
                
                // Screen position (account for camera offset)
                var screenX = particle.pos.x - ig.game.screen.x;
                var screenY = particle.pos.y - ig.game.screen.y;
                
                // Draw particle as a circle with gradient
                var radius = Math.max(1, particle.size); // Ensure radius is at least 1
                var gradient =  this.ctx.createRadialGradient(screenX, screenY, 0, screenX, screenY, radius);
                
                // Use configurable colors
                var innerColor = this.smokeConfig.colors.inner;
                var middleColor = this.smokeConfig.colors.middle;
                var outerColor = this.smokeConfig.colors.outer;
                
                gradient.addColorStop(0, 'rgba(' + innerColor[0] + ', ' + innerColor[1] + ', ' + innerColor[2] + ', ' + alpha + ')');
                gradient.addColorStop(0.7, 'rgba(' + middleColor[0] + ', ' + middleColor[1] + ', ' + middleColor[2] + ', ' + (alpha * 0.5) + ')');
                gradient.addColorStop(1, 'rgba(' + outerColor[0] + ', ' + outerColor[1] + ', ' + outerColor[2] + ', 0)');
                
                 this.ctx.fillStyle = gradient;
                 this.ctx.beginPath();
                 this.ctx.arc(screenX, screenY, radius, 0, Math.PI * 2);
                 this.ctx.fill();
            }
            
             this.ctx.restore();
        },
        // #endregion
        // =================================================================================
        // #region PROXIMITY DETECTION SYSTEM
        // =================================================================================
        updateProximityDetection: function () {
            // Update timers
            if (this.proximityCheckTimer > 0) {
                this.proximityCheckTimer -= ig.system.tick * 60;
            }
            if (this.proximityHonkTimer > 0) {
                this.proximityHonkTimer -= ig.system.tick * 60;
            }

            // Only check proximity periodically for performance
            if (this.proximityCheckTimer <= 0) {
                this.checkProximityToOtherTrucks();
                this.proximityCheckTimer = this.proximityCheckCooldown;
            }
        },

        checkProximityToOtherTrucks: function () {
            var allTrucks = ig.game.getEntitiesByType(EntityTruck);
            var currentlyNearbyTrucks = [];
            var thisCenter = this.getCenterPoint();

            for (var i = 0; i < allTrucks.length; i++) {
                var otherTruck = allTrucks[i];

                // Skip self
                if (otherTruck === this) continue;

                // Skip trucks that are spawning, parked, or exited
                if (otherTruck.isSpawning) continue;

                var otherCenter = otherTruck.getCenterPoint();
                var distance = ig.utils.distanceBetweenPoints(thisCenter, otherCenter);

                if (distance <= this.proximityThreshold) {
                    currentlyNearbyTrucks.push(otherTruck);
                }
            }

            // Check for new trucks entering proximity
            for (var j = 0; j < currentlyNearbyTrucks.length; j++) {
                var nearbyTruck = currentlyNearbyTrucks[j];
                var wasAlreadyNearby = this.nearbyTrucks.indexOf(nearbyTruck) !== -1;

                if (!wasAlreadyNearby) {
                    this.onTruckEnterProximity(nearbyTruck);
                }
            }

            // Check for trucks leaving proximity
            for (var k = 0; k < this.nearbyTrucks.length; k++) {
                var previouslyNearbyTruck = this.nearbyTrucks[k];
                var isStillNearby = currentlyNearbyTrucks.indexOf(previouslyNearbyTruck) !== -1;

                if (!isStillNearby) {
                    this.onTruckExitProximity(previouslyNearbyTruck);
                }
            }

            // Update the nearby trucks list
            this.nearbyTrucks = currentlyNearbyTrucks;

            // Update visual warning state
            this.updateProximityWarningState();
        },

        onTruckEnterProximity: function (otherTruck) {
            // Play honk sound if cooldown has expired
            if (this.proximityHonkTimer <= 0 && ig.soundHandler && ig.soundHandler.sfxPlayer) {
                // Check if honk sound exists, fallback to existing sounds if not
                try {
                    // console.log("Playing honk sound");
                    ig.soundHandler.sfxPlayer.play("honk");
                } catch (e) {
                    console.warn(e);
                }
                this.proximityHonkTimer = this.proximityHonkCooldown;
            }
        },

        onTruckExitProximity: function (otherTruck) {
            // This method can be used for any cleanup when trucks move apart if needed
        },

        updateProximityWarningState: function () {
            var shouldShowWarning = this.nearbyTrucks.length > 0;

            if (shouldShowWarning && !this.isInProximityWarning) {
                // Enter warning state - change to red appearance
                this.enterProximityWarningState();
            } else if (!shouldShowWarning && this.isInProximityWarning) {
                // Exit warning state - revert to normal appearance
                this.exitProximityWarningState();
            }
        },

        enterProximityWarningState: function () {
            if (this.isInProximityWarning) return; // Already in warning state

            // Store the original truck type
            this.originalTruckType = this.truckType;

            // Change to warning (red) version by replacing the direction suffix with "w"
            var warningType = this.getWarningTruckType(this.truckType);
            if (warningType && this.anims[warningType]) {
                this.truckType = warningType;
                this.currentAnim = this.anims[warningType];
                this.isInProximityWarning = true;
            }
        },

        exitProximityWarningState: function () {
            if (!this.isInProximityWarning) return; // Not in warning state

            // Revert to original truck type
            if (this.originalTruckType && this.anims[this.originalTruckType]) {
                this.truckType = this.originalTruckType;
                this._angle = this.angle;
                this._animAngle = this.currentAnim.angle;
                this.currentAnim = this.anims[this.originalTruckType];
                this.angle = this._angle;
                this.currentAnim.angle = this._animAngle;
                this.isInProximityWarning = false;
                this.originalTruckType = null;
            }
        },

        getWarningTruckType: function (currentType) {
            // Convert any truck type to its warning (red) version
            // Examples: "truck1n" -> "truck1w", "truck2s" -> "truck2w", etc.
            if (!currentType) return null;

            var baseType = currentType.substring(0, currentType.length - 1); // Remove last character (direction)
            return baseType + "w"; // Add "w" for warning/red
        },

        getCenterPoint: function () {
            return {
                x: this.pos.x + this.size.x / 2,
                y: this.pos.y + this.size.y / 2
            };
        },

        getNearestTruckDistance: function () {
            var allTrucks = ig.game.getEntitiesByType(EntityTruck);
            var thisCenter = this.getCenterPoint();
            var nearestDistance = Infinity;

            for (var i = 0; i < allTrucks.length; i++) {
                var otherTruck = allTrucks[i];

                // Skip self
                if (otherTruck === this) continue;

                // Skip trucks that are spawning, parked, or exited
                if (otherTruck.isSpawning || otherTruck.truckState === 'exited') continue;

                var otherCenter = otherTruck.getCenterPoint();
                var distance = ig.utils.distanceBetweenPoints(thisCenter, otherCenter);

                if (distance < nearestDistance) {
                    nearestDistance = distance;
                }
            }

            return nearestDistance === Infinity ? 'N/A' : Math.round(nearestDistance);
        },
        // #endregion
        // =================================================================================
        // #region INPUT HANDLING
        // =================================================================================
        handleInput: function () {
            // Disable input if parked (and not ready to exit) or currently parking
            if ((this.isParked && this.truckState !== 'exit-ready') || this.isParking) {
                if (this.isClicked || this.isDragging) {
                    this.onReleased(); // Cancel any active drag
                }
                return;
            }

            if (ig.input.pressed('click')) {
                var p = ig.game.io.getClickPos();
                if (this.containsPointer(p)) {
                    this.onClicked();
                }
            }

            if (ig.input.state('click') && this.isClicked) {
                this.clicking();
            }

            if (ig.input.released('click')) {
                if (this.isClicked) {
                    this.onReleased();
                }
            }
        },

        onClicked: function () {
            this.isClicked = true;
            this.clickTime = ig.system.clock.delta();
            this._startNewPathDrawing();
        },

        _startNewPathDrawing: function () {
            // this.vel.x = 0;
            // this.vel.y = 0;

            this.isCurrentPathInvalid = false;
            if (this.pathHead) { this.pathHead.kill(); this.pathHead = null; }

            var p = ig.game.io.getClickPos();
            // If truck is parked and ready to exit, the path starts from the truck's pivot, not the click position.
            var pathHeadStartPos = (this.isParked && this.truckState === 'exit-ready') || this.isFollowingTrajectory() ?
                                     this.getPivotPoint() : p;

            this.pathHead = ig.game.spawnEntity(EntityPathHead, pathHeadStartPos.x, pathHeadStartPos.y, { radius: this.pathHeadRadius });

            if (ig.currentCtrl) ig.currentCtrl.currentVehicle = this;

            // Reset trajectory state
            this.trajectoryComplete = false;
            this.trajectorySnapped = false;
            this.finalDirectionX = 0;
            this.finalDirectionY = 0;
            this.trajectory = [];
            this.isDragging = false;

            this.dashLineAlpha = 0.8;
            this.solidLineAlpha = 0;

            this.initialDragPos = { x: p.x, y: p.y };
            this.currentDragPos = { x: p.x, y: p.y };
        },

        clicking: function () {
            if (!this.isClicked) return;

            var mousePos = ig.game.io.getClickPos();
            this.currentDragPos.x = mousePos.x;
            this.currentDragPos.y = mousePos.y;

            var dragDist = ig.utils.distanceBetween(this.initialDragPos.x, this.initialDragPos.y, mousePos.x, mousePos.y);

            if (!this.isDragging && dragDist > this.minimumDragDistance) {
                 this.isDragging = true;
            }
            if (!this.pathHead && this.isCurrentPathInvalid) {
                return; // Path is invalid, do nothing until next click
            }
            if (this.pathHead) {
                this.pathHead.updateSATShape(mousePos);
                if (this.isDragging) {
                    if (this.checkPathHeadCollisions()) {
                        this.isCurrentPathInvalid = true;
                        this.trajectory = [];
                        this.pathHead.kill();
                        this.pathHead = null;
                        return;
                    } else {
                        this.isCurrentPathInvalid = false;
                    }
                    if (!this.isCurrentPathInvalid) {
                        this.addCoordinate(mousePos, true);

                        // Check if the path snaps to a parking slot
                        if (this.trajectory.length > 0 && !this.isCurrentPathInvalid) {
                            var snapped = this.attemptSnapToParkingSlot(this.trajectory[this.trajectory.length - 1]);
                            if (snapped && this.calculateTrajectoryDistance() >= this.minTrajectoryDistance) {
                                this.finalizeSnappedTrajectory();
                            }
                        }
                    }
                }
            }
        },

        onReleased: function () {
            if (!this.isClicked) return;

            this.isClicked = false;
            this.isDragging = false;
            if (this.pathHead) { this.pathHead.kill(); this.pathHead = null; }

            if (this.isCurrentPathInvalid && this.trajectory.length > 0) {
                this.trajectory = []; // Discard invalid path
                this.currentWaypointIndex = 0;
            } else if (this.trajectory.length > 0) {
                var trajectoryDistance = this.calculateTrajectoryDistance();
                if (trajectoryDistance < this.minTrajectoryDistance) {
                    this.trajectory = []; // Discard path that is too short
                    this.currentWaypointIndex = 0;
                } else {
                    // Smooth the final path
                    // if (this.trajectory.length > 0) {
                    //     this.trajectory = this.pathSmoother.chaikin(this.trajectory, 1, 0.1);
                    // }
                    
                    this.dashLineAlpha = 0;
                    this.solidLineAlpha = 0.8;
                }
            }
            
            this.isCurrentPathInvalid = false;

            if (ig.currentCtrl && ig.currentCtrl.currentVehicle === this) {
                ig.currentCtrl.currentVehicle = null;
            }
        },

        containsPointer: function (p) {
            return this.vertices ? ig.utils.pointInPolygon(p, this.vertices) : false;
        },
        // #endregion
        // =================================================================================
        // #region MOVEMENT
        // =================================================================================
        updateSpawning: function () {
            var currentSpeed = this.speed;
            // Move the truck onto the screen from its spawn edge
            switch (this.direction) {
                case 0: this.vel.x = currentSpeed; this.vel.y = 0; if (this.pos.x >= 0) this.isSpawning = false; break;
                case 1: this.vel.x = 0; this.vel.y = currentSpeed; if (this.pos.y >= 0) this.isSpawning = false; break;
                case 2: this.vel.x = -currentSpeed; this.vel.y = 0; if (this.pos.x <= ig.system.width - this.size.x) this.isSpawning = false; break;
                case 3: this.vel.x = 0; this.vel.y = -currentSpeed; if (this.pos.y <= ig.system.height - this.size.y) this.isSpawning = false; break;
            }
        },

        updateTrajectoryMovement: function () {
            var targetPoint = this.getCurrentWaypoint();
            if (!targetPoint) {
                this.finalizeTrajectoryMovement();
                return;
            }
        
            // If this is the last point, prepare for the final approach.
            if (this.isLastWaypoint()) {
                this.prepareForLastWaypoint(targetPoint);
            }

            this.goTo(targetPoint.x, targetPoint.y);

            var angleDiff = Math.abs(this.getShortestRotation(this.angle, this.targetAngle));
            
            // Speed modifier logic:
            // The closer the angle difference is to 0, the closer the modifier is to 1.
            // The further away, it drops off sharply.
            // We use Math.PI / 2 (90 degrees) as the point of maximum slowdown.
            var turnSeverity = Math.min(1, angleDiff / (Math.PI / 2)); // Normalize to 0-1 based on a 90-degree turn
            var speedModifier = Math.pow(1 - turnSeverity, 2); // Square the result for a non-linear curve

            // Ensure a minimum speed to prevent the truck from stopping completely
            speedModifier = Math.max(0.1, speedModifier);
            
            this.moveInFacingDirection(this.speed * speedModifier);

            if (this.checkWaypointReached(targetPoint)) {
                this.advanceWaypoint();
                if (this.isTrajectoryEmpty()) {
                    this.finalizeTrajectoryMovement();
                }
            }
        },

        goTo: function (x, y) {
            // this.currentTarget = { x: x, y: y };
            var pivotPoint = this.getPivotPoint();
            
            // Calculate the angle from the pivot point to the target
            this.targetAngle = Math.atan2(y - pivotPoint.y, x - pivotPoint.x);
            this.targetAngle = this.normalizeAngle(this.targetAngle);
        },

        moveToTarget: function (targetPoint) {
            this.goTo(targetPoint.x, targetPoint.y);
        },

        moveInFacingDirection: function (speed) {
            var moveSpeed = speed || this.speed;
            
            this.vel.x = Math.cos(this.angle) * moveSpeed;
            this.vel.y = Math.sin(this.angle) * moveSpeed;
        },

        updateAngleSimple: function () {
            this.angleChangedThisFrame = false;
            
            var angleDiff = this.getShortestRotation(this.angle, this.targetAngle);
            
            // If there's a significant difference, rotate towards the target angle
            if (Math.abs(angleDiff) > 0.01) { // A small threshold to prevent jittering
                this.angle += angleDiff * this.rotationSpeed;
                this.angle = this.normalizeAngle(this.angle);
                this.angleChangedThisFrame = true;
            }
        },

        normalizeAngle: function (angle) {
            if (!isFinite(angle)) return 0;
            
            // Keep the angle between 0 and 2*PI
            var n = Math.floor(angle / (Math.PI * 2));
            angle = angle - n * (Math.PI * 2);
            if (angle < 0) angle += (Math.PI * 2);
            return angle;
        },

        getShortestRotation: function (currentAngle, targetAngle) {
            currentAngle = this.normalizeAngle(currentAngle);
            targetAngle = this.normalizeAngle(targetAngle);
            
            var diff = targetAngle - currentAngle;
            
            // Adjust the difference to be within -PI and PI to find the shortest path
            while (diff > Math.PI) diff -= Math.PI * 2;
            while (diff < -Math.PI) diff += Math.PI * 2;
            
            return diff;
        },

        getDirectionFromAngle: function () {
            return {
                x: Math.cos(this.angle),
                y: Math.sin(this.angle)
            };
        },
        
        updatePivotOffset: function () {
            // The pivot point is slightly in front of the center, for more realistic turning
            var offsetDistance = this.size.y * this.pivotOffsetRatio;
            this.pivotOffset.x = Math.cos(this.angle) * offsetDistance;
            this.pivotOffset.y = Math.sin(this.angle) * offsetDistance;
        },

        getPivotPoint: function () {
            var centerX = this.pos.x + this.size.x / 2;
            var centerY = this.pos.y + this.size.y / 2;
            return {
                x: centerX + this.pivotOffset.x,
                y: centerY + this.pivotOffset.y
            };
        },

        createAttackVertices: function () {
            if (!this.useCustomVertices || !this.customVertices || !this.truckType || !this.customVertices[this.truckType]) {
                var halfWidth = this.size.x / 2;
                var frontHeight = this.size.y * this.attackBoxPercentage;
                var halfHeight = this.size.y / 2;
                return [
                    { x: -halfWidth, y: -halfHeight },
                    { x: halfWidth, y: -halfHeight },
                    { x: halfWidth, y: -halfHeight + frontHeight },
                    { x: -halfWidth, y: -halfHeight + frontHeight }
                ];
            }

            var baseVertices = this.customVertices[this.truckType];
            
            var minY = Infinity, maxY = -Infinity, minX = Infinity, maxX = -Infinity;

            for (var i = 0; i < baseVertices.length; i++) {
                minY = Math.min(minY, baseVertices[i].y);
                maxY = Math.max(maxY, baseVertices[i].y);
                minX = Math.min(minX, baseVertices[i].x);
                maxX = Math.max(maxX, baseVertices[i].x);
            }

            var height = maxY - minY;
            var attackBoxHeight = height * this.attackBoxPercentage;

            return [
                { x: minX, y: minY },
                { x: maxX, y: minY },
                { x: maxX, y: minY + attackBoxHeight },
                { x: minX, y: minY + attackBoxHeight }
            ];
        },

        updateRotatedVertices: function () {
            var centerX = this.pos.x + this.size.x / 2;
            var centerY = this.pos.y + this.size.y / 2;
            var spriteAngle = this.angle + this.angleOffset; // Apply visual correction

            if (this.useCustomVertices && this.customVertices && this.truckType && this.customVertices[this.truckType]) {
                var vertexSet = this.customVertices[this.truckType];
                this.vertices = [];
                for (var i = 0; i < vertexSet.length; i++) {
                    var vertex = vertexSet[i];
                    // Rotate each custom vertex around the entity's center
                    this.vertices.push(
                        ig.utils.rotatePoint(centerX + vertex.x, centerY + vertex.y, centerX, centerY, spriteAngle)
                    );
                }
            } else {
                // Fallback to a simple box shape
                var halfWidth = this.size.x / 2;
                var halfHeight = this.size.y / 2;
                this.vertices = [
                    ig.utils.rotatePoint(centerX - halfWidth, centerY - halfHeight, centerX, centerY, spriteAngle),
                    ig.utils.rotatePoint(centerX + halfWidth, centerY - halfHeight, centerX, centerY, spriteAngle),
                    ig.utils.rotatePoint(centerX + halfWidth, centerY + halfHeight, centerX, centerY, spriteAngle),
                    ig.utils.rotatePoint(centerX - halfWidth, centerY + halfHeight, centerX, centerY, spriteAngle)
                ];
            }

            // Create and rotate attack vertices
            var attackVertexSet = this.createAttackVertices();
            this.attackVertices = []; // Clear before pushing rotated vertices
            for (var j = 0; j < attackVertexSet.length; j++) {
                var attackVertex = attackVertexSet[j];
                this.attackVertices.push(
                    ig.utils.rotatePoint(centerX + attackVertex.x, centerY + attackVertex.y, centerX, centerY, spriteAngle)
                );
            }
            
            this.updateSATShape();
        },

        updateSATShape: function () {
            this.shape = new ig.SAT.Shape(this.vertices);
            if (this.attackVertices.length > 0) {
                this.attackShape = new ig.SAT.Shape(this.attackVertices);
            }
        },

        getActiveTruckWidth: function () {
            if (this.useCustomVertices && this.customVertices && this.truckType && this.customVertices[this.truckType]) {
                var vertexSet = this.customVertices[this.truckType];
                var minX = Infinity, maxX = -Infinity;
                for (var i = 0; i < vertexSet.length; i++) {
                    if (vertexSet[i].x < minX) minX = vertexSet[i].x;
                    if (vertexSet[i].x > maxX) maxX = vertexSet[i].x;
                }
                return maxX - minX;
            }
            return this.size.x;
        },

        updatePathHeadRadius: function () {
            var width = this.getActiveTruckWidth();
            this.pathHeadRadius = width / 2 * 0.8;
        },
        // #endregion
        // =================================================================================
        // #region TRAJECTORY MANAGEMENT
        // =================================================================================
        isFollowingTrajectory: function () {
            return this.trajectory.length > 0 && !this.trajectoryComplete;
        },
        
        hasCompletedTrajectoryButNotMovedOn: function () {
            return this.trajectory.length === 0 && this.trajectoryComplete && !this.isClicked;
        },

        isTrajectoryEmpty: function () {
            return this.trajectory.length === 0;
        },

        finalizeTrajectoryMovement: function () {
            // If the mouse is still held down, immediately start drawing a new path.
            if (this.isClicked) {
                this._startNewPathDrawing();
            } else {
                this.trajectoryComplete = true;
                // If a final angle was calculated, set it as the target to smoothly rotate towards.
                if (this.finalAngleForMovement !== null) {
                    this.targetAngle = this.finalAngleForMovement;
                    this.finalAngleForMovement = null; // Reset for next path
                }
            }
        },

        clearTrajectory: function () {
            this.trajectory = [];
            this.trajectoryComplete = true;
            this.isClicked = false;
            this.currentWaypointIndex = 0;
            
            if (this.pathHead) {
                this.pathHead.kill();
                this.pathHead = null;
            }
        },

        addCoordinate: function (p, interpolate) {
            if (this.isCurrentPathInvalid || !this.pathHead) {
                return;
            }

            var segmentInvalid = false; // Initialize segment validity for current point 'p'

            // LOGIC FOR TRUCK EXITING PARKING SPOT
            if (this.isParked && this.truckState === 'exit-ready') {
                if (this.trajectory.length === 0) { // This is the first point of the path
                    var truckPivot = this.getPivotPoint();

                    // Check for attackShape and its vertices
                    if (!this.attackShape || !this.attackShape.pointList || this.attackShape.pointList.length < 2) {
                        console.warn("EntityTruck.addCoordinate: Truck attackShape or its pointList not available/sufficient for front line check during exit.");
                        segmentInvalid = true;
                    } else if (!this.satInstance || typeof this.satInstance.simpleShapeIntersect !== 'function') {
                        console.warn("EntityTruck.addCoordinate: SAT tools not available for front line check during exit.");
                        segmentInvalid = true;
                    } else {
                        // Define front line using attackShape's first two vertices
                        // This assumes that this.attackShape.pointList[0] and [1] form the front edge.
                        var frontLineP1 = this.attackShape.pointList[0];
                        var frontLineP2 = this.attackShape.pointList[1];

                        var pathSegmentShape = new ig.SAT.Shape([
                            { x: truckPivot.x, y: truckPivot.y }, { x: p.x, y: p.y }
                        ]);
                        // Ensure frontLineP1 and frontLineP2 are actual Vector2D objects for SAT.Shape
                        // The pointList from SAT.Shape should already contain SAT.Vector2D instances.
                        var truckFrontShape = new ig.SAT.Shape([
                            { x: frontLineP1.x, y: frontLineP1.y }, { x: frontLineP2.x, y: frontLineP2.y }
                        ]);

                        if (!this.satInstance.simpleShapeIntersect(pathSegmentShape, truckFrontShape)) {
                            segmentInvalid = true; // Path does not cross the front line of attackShape
                        }
                    }
                }
            }

            // LOGIC FOR MINIMUM TRAJECTORY DISTANCE (GENERAL CASE, NOT EXITING)
            // This applies if the segment wasn't already invalidated by the exit check.
            if (!segmentInvalid && this.trajectory.length === 0 && !(this.isParked && this.truckState === 'exit-ready')) {
                var startPointForMinDist = this.getPivotPoint();
                var distFromTruck = ig.utils.distanceBetween(startPointForMinDist.x, startPointForMinDist.y, p.x, p.y);
                if (distFromTruck < this.minTrajectoryDistance) {
                    segmentInvalid = true; // Path is too short
                }
            }

            // FINAL DECISION BASED ON CHECKS
            if (segmentInvalid) {
                this.isCurrentPathInvalid = true;
                // Visual feedback or cleanup for invalid start
                // if (this.pathHead) { this.pathHead.kill(); this.pathHead = null; } // Example
                return; // Stop processing if current point makes path invalid
            }

            // If all checks pass, path is valid so far
            this.isCurrentPathInvalid = false;

            // ADD POINT TO TRAJECTORY
            var lastTrajectoryPoint = this.trajectory.length === 0 ? this.getPivotPoint() : this.trajectory[this.trajectory.length - 1];
            var distBetweenInterPoints = ig.utils.distanceBetween(lastTrajectoryPoint.x, lastTrajectoryPoint.y, p.x, p.y);
            var minDistBetweenInterPoints = 17;

            if (distBetweenInterPoints > minDistBetweenInterPoints) {
                // Interpolate points to create a smoother path if the distance is large
                if (interpolate && distBetweenInterPoints > this.halfW) {
                    var numSegments = Math.floor(distBetweenInterPoints / (this.halfW * 0.8));
                    numSegments = Math.max(1, numSegments);
                    var dx = (p.x - lastTrajectoryPoint.x) / numSegments;
                    var dy = (p.y - lastTrajectoryPoint.y) / numSegments;

                    for (var i = 1; i < numSegments; i++) {
                        this.trajectory.push({ x: lastTrajectoryPoint.x + i * dx, y: lastTrajectoryPoint.y + i * dy });
                    }
                }
                this.trajectory.push({ x: p.x, y: p.y });
            }
        },

        finalizeSnappedTrajectory: function () {
            // if (this.trajectory.length > 0) {
            //     this.trajectory = this.pathSmoother.chaikin(this.trajectory, 1, 0.1);
            // }
            this.trajectoryComplete = false;
            this.isClicked = false;
            this.isDragging = false;
            this.dashLineAlpha = 0;
            this.solidLineAlpha = 0.8;
            this.trajectorySnapped = true;
            if (this.pathHead) { this.pathHead.kill(); this.pathHead = null; }
            if (ig.currentCtrl && ig.currentCtrl.currentVehicle === this) {
                ig.currentCtrl.currentVehicle = null;
            }
            if (ig.soundHandler.sfxPlayer.soundList.landing) {
                ig.soundHandler.sfxPlayer.play('landing');
            }
        },

        calculateTrajectoryDistance: function () {
            if (this.trajectory.length === 0) return 0;
            var totalDistance = 0;
            var startPoint = this.getPivotPoint();
            for (var i = 0; i < this.trajectory.length; i++) {
                var point = this.trajectory[i];
                totalDistance += ig.utils.distanceBetweenPoints(startPoint, point);
                startPoint = point;
            }
            return totalDistance;
        },

        checkPathHeadCollisions: function () {
            if (!this.pathHead || !this.pathHead.getSATShape()) return false;
            
            var pathHeadShape = this.pathHead.getSATShape();
            var gameController = ig.currentCtrl;
            if (!gameController || !gameController.collisionShapesData) return false;
            
            var pathHeadPos = this.pathHead.pos;
            var radius = this.pathHead.getRadius();
            var pathHeadAABB = {
                minX: pathHeadPos.x - radius,
                minY: pathHeadPos.y - radius,
                maxX: pathHeadPos.x + radius,
                maxY: pathHeadPos.y + radius
            };
            
            // Check against static level geometry (buildings)
            for (var i = 0; i < gameController.collisionShapesData.length; i++) {
                var levelShapeData = gameController.collisionShapesData[i];
                if (levelShapeData.type === LevelData.COLLISION_TYPES.BUILDING) {
                    // Broad phase: AABB check for performance
                    if (gameController.checkAABBOverlap(pathHeadAABB, levelShapeData.aabb)) {
                        // Narrow phase: Precise SAT check
                        if (this.satInstance.simpleShapeIntersect(pathHeadShape, levelShapeData.shape, true)) {
                            return true;
                        }
                    }
                }
            }
            
            // Check against other trucks
            var allTrucks = ig.game.getEntitiesByType(EntityTruck);
            for (var j = 0; j < allTrucks.length; j++) {
                var otherTruck = allTrucks[j];
                
                if (!otherTruck || !otherTruck.shape) {
                    continue;
                }
                
                var isOwnTruck = (otherTruck === this);
                
                // Only check for collision with itself if it's not parked.
                // We also wait until the path is a certain distance away from the truck
                // to avoid immediate self-collision when starting a new path.
                if (isOwnTruck && this.truckState == 'none' && this.calculateTrajectoryDistance() > this.size.y * 0.75) {
                    if (otherTruck && typeof otherTruck.updateRotatedVertices === 'function') {
                        otherTruck.updateRotatedVertices();
                    }
                    
                    var truckAABB = gameController.calculateAABB(otherTruck.vertices);
                    if (gameController.checkAABBOverlap(pathHeadAABB, truckAABB)) {
                        if (this.satInstance.simpleShapeIntersect(pathHeadShape, otherTruck.shape, true)) {
                            return true;
                        }
                    }
                }
            }
            
            return false;
        },
        // #endregion
        // =================================================================================
        // #region WAYPOINT HANDLING
        // =================================================================================
        getCurrentWaypoint: function () {
            return this.trajectory.length > 0 ? this.trajectory[0] : null;
        },

        getNextWaypoint: function () {
            return this.trajectory.length > 1 ? this.trajectory[1] : null;
        },

        isLastWaypoint: function () {
            return this.trajectory.length === 1;
        },

        advanceWaypoint: function () {
            if (this.trajectory.length > 0) {
                this.trajectory.shift();
            }
        },

        checkWaypointReached: function (waypoint) {
            if (!waypoint) return false;
            
            var pivotPoint = this.getPivotPoint();
            var dx = waypoint.x - pivotPoint.x;
            var dy = waypoint.y - pivotPoint.y;
            var distance = Math.sqrt(dx * dx + dy * dy);
            
            var tolerance = this.isLastWaypoint() ? 5 : 12;
            return distance < tolerance;
        },

        // passTarget: function () {
        //     if (!this.currentTarget) return false;
            
        //     var pivotPoint = this.getPivotPoint();
        //     // Check if the truck has moved past its target
        //     return (
        //         (this.vel.x > 0 && pivotPoint.x > this.currentTarget.x) ||
        //         (this.vel.x < 0 && pivotPoint.x < this.currentTarget.x) ||
        //         (this.vel.y > 0 && pivotPoint.y > this.currentTarget.y) ||
        //         (this.vel.y < 0 && pivotPoint.y < this.currentTarget.y)
        //     );
        // },

        prepareForLastWaypoint: function (lastPoint) {
            var pivotPoint = this.getPivotPoint();
            this.targetAngle = this.normalizeAngle(Math.atan2(lastPoint.y - pivotPoint.y, lastPoint.x - pivotPoint.x));
            
            var dirX = lastPoint.x - pivotPoint.x;
            var dirY = lastPoint.y - pivotPoint.y;
            var length = Math.sqrt(dirX * dirX + dirY * dirY);
            if (length > 0) {
                this.finalDirectionX = dirX / length;
                this.finalDirectionY = dirY / length;
                this.finalAngleForMovement = Math.atan2(this.finalDirectionY, this.finalDirectionX);
            }
        },

        setMovementToFinalDirection: function () {
            this.targetAngle = this.finalAngleForMovement;
            this.vel.x = this.finalDirectionX * this.speed;
            this.vel.y = this.finalDirectionY * this.speed;
        },

        initializeWaypointTimeout: function () {
            this.currentWaypointStartTime = ig.system.clock.delta();
        },

        isStuckOnWaypoint: function () {
            if (!this.currentWaypointStartTime) {
                this.initializeWaypointTimeout();
                return false;
            }
            return (ig.system.clock.delta() - this.currentWaypointStartTime) > this.waypointTimeoutDuration;
        },

        resetWaypointTimeout: function () {
            this.currentWaypointStartTime = null;
        },
        // #endregion
        // =================================================================================
        // #region PARKING LOGIC
        // =================================================================================
        isParkingEngaged: function () {
            return this.isParking || this.isParked;
        },

        beginParking: function (parkingSlotEntity) {
            if (parkingSlotEntity && parkingSlotEntity instanceof EntityParkingSlot &&
                parkingSlotEntity.type === LevelData.COLLISION_TYPES.PARKING_SLOT) {
                if (this.color === parkingSlotEntity.color && !parkingSlotEntity.isCurrentlyOccupied()) {
                    this.beginParkingInternal(parkingSlotEntity);
                } else {
                    this.needsCollisionRestore = true;
                }
            } else {
                this.needsCollisionRestore = true;
            }
        },

        beginParkingInternal: function (parkingSlotEntity) {
            if (!parkingSlotEntity || !(parkingSlotEntity instanceof EntityParkingSlot)) {
                console.error("Truck.beginParking: Invalid parkingSlotEntity provided.");
                this.needsCollisionRestore = true;
                return;
            }
            
            if (parkingSlotEntity.isCurrentlyOccupied()) {
                console.warn("Truck: Attempting to park in already occupied slot (checked via entity):", parkingSlotEntity.name);
            }
            
            if (this.isParking || this.isParked) {
                return;
            }
            
            if (parkingSlotEntity.color !== this.color) {
                console.warn("Truck.beginParking: Color mismatch! Truck: " + this.color + ", Slot: " + parkingSlotEntity.color);
            }
            
            var slotDataForEntity = null;
            for (var i = 0; i < this.parkingSlots.length; i++) {
                if (this.parkingSlots[i].name === parkingSlotEntity.name && this.parkingSlots[i].slotEntity === parkingSlotEntity) {
                    slotDataForEntity = this.parkingSlots[i];
                    break;
                }
            }
            
            if (!slotDataForEntity) {
                this.needsCollisionRestore = true;
                return;
            }
            
            var angle1 = slotDataForEntity.orientationAngle;
            
            this.parkingTargetSlotInfo = ig.copy(slotDataForEntity);
            // The final angle should be opposite to the slot's orientation
            this.parkingTargetSlotInfo.finalParkingAngle = this.normalizeAngle(angle1 + Math.PI);
            
            this.isParking = true;
            this.truckState = 'parking';
            this.clearTrajectory();
            this.vel.x = 0;
            this.vel.y = 0;
        },

        updateParking: function () {
            if (this.isParking) {
                this.handleParking();
            } else if (this.isParked) {
                this.handleParkedState();
            }
        },

        handleParking: function () {
            if (!this.parkingTargetSlotInfo || !this.parkingTargetSlotInfo.slotEntity) {
                console.warn("Truck.handleParking: parkingTargetSlotInfo or its slotEntity is missing. Aborting.");
                this.finishParking(true);
                return;
            }

            var targetCenter = this.parkingTargetSlotInfo.center;
            var targetAngle = this.parkingTargetSlotInfo.finalParkingAngle;
            var currentCenter = { x: this.pos.x + this.size.x / 2, y: this.pos.y + this.size.y / 2 };
            var distanceToTarget = ig.utils.distanceBetweenPoints(currentCenter, targetCenter);
            var angleToTargetCenter = ig.utils.angleBetweenPoints(currentCenter, targetCenter);
            var angleDiff = this.getShortestRotation(this.angle, targetAngle);

            // Rotate towards the final parking angle
            if (Math.abs(angleDiff) > 0.01) {
                this.angle += angleDiff * this.parkingRotationSpeed;
                this.angle = this.normalizeAngle(this.angle);
                this.angleChangedThisFrame = true;
            }

            // Move towards the center of the parking spot
            var speedMultiplier = 1;
            if (distanceToTarget > this.parkingThreshold) {
                // Slow down if not facing the right direction
                speedMultiplier = (Math.abs(angleDiff) > Math.PI / 4) ? 0.3 : 1;
                this.vel.x = Math.cos(angleToTargetCenter) * this.parkingSpeed * speedMultiplier;
                this.vel.y = Math.sin(angleToTargetCenter) * this.parkingSpeed * speedMultiplier;
            } else {
                // Close enough, stop moving and wait for rotation to finish
                this.vel.x = 0; this.vel.y = 0;
                if (Math.abs(angleDiff) <= 0.05) {
                    this.finishParking(false);
                }
            }
        },

        handleParkedState: function () {
            this.vel.x = 0; this.vel.y = 0;
            switch (this.truckState) {
                case 'waiting':
                    // Wait for a few seconds before being ready to exit
                    this.parkingWaitTimer += ig.system.tick;
                    if (this.parkingWaitTimer >= this.parkingWaitTime) {
                        this.truckState = 'exit-ready';
                        // Turn the truck around to face outwards
                        this.angle = this.normalizeAngle(this.angle + Math.PI);
                        this.targetAngle = this.angle;
                        this.angleChangedThisFrame = true;
                        if (ig.soundHandler.sfxPlayer.soundList.finish) {
                            ig.soundHandler.sfxPlayer.play('finish');
                        }
                    }
                    break;
                case 'exit-ready':
                    // If the user draws a path, start exiting
                    if (this.trajectory.length > 0 && !this.isClicked) {
                        this.truckState = 'exiting';
                        this.isParked = false;
                        if (this._originalCollidesWith){
                             this._originalCollidesWith = this._originalCollidesWith.filter(function (type) {
                                  return type !== (LevelData.COLLISION_TYPES.BOUNDARY || 2);
                             });
                       }
                        this.needsCollisionRestore = true;
                    }
                    break;
            }
        },

        finishParking: function (forceAbort) {
            var slotEntity = this.parkingTargetSlotInfo ? this.parkingTargetSlotInfo.slotEntity : null;

            if (slotEntity && this.parkingTargetSlotInfo && !forceAbort) {
                slotEntity.setOccupied(true);

                // Snap to the final position and angle
                this.pos.x = this.parkingTargetSlotInfo.center.x - this.size.x / 2;
                this.pos.y = this.parkingTargetSlotInfo.center.y - this.size.y / 2;
                this.angle = this.parkingTargetSlotInfo.finalParkingAngle;
                this.targetAngle = this.angle;
                this.angleChangedThisFrame = true;

                this.isParked = true;
                this.parkingWaitTimer = 0;
                this.truckState = 'waiting';

            } else {
                // Parking failed or was aborted
                if (slotEntity) {
                    slotEntity.setOccupied(false);
                } else if (this.parkingTargetSlotInfo && this.parkingTargetSlotInfo.name) {
                    // Fallback to find the slot by name if the entity reference was lost
                    var fallbackSlot = ig.game.getEntityByName(this.parkingTargetSlotInfo.name);
                    if (fallbackSlot && fallbackSlot instanceof EntityParkingSlot) {
                         fallbackSlot.setOccupied(false);
                    }
                }
                this.isParked = false;
                this.truckState = 'none';
                this.needsCollisionRestore = true;
            }
            if (ig.soundHandler.sfxPlayer.soundList.landed) {
                ig.soundHandler.sfxPlayer.play('landed');
            }
            this.isParking = false;
            this.vel.x = 0; this.vel.y = 0;
        },
        
        vacateSlot: function () {
            var slotEntityToVacate = null;
            if (this.parkingTargetSlotInfo && this.parkingTargetSlotInfo.slotEntity) {
                slotEntityToVacate = this.parkingTargetSlotInfo.slotEntity;
            } else if (this.intendedParkingSlotName) {
                var foundByName = ig.game.getEntityByName(this.intendedParkingSlotName);
                if (foundByName instanceof EntityParkingSlot) {
                    slotEntityToVacate = foundByName;
                }
            }

            if (slotEntityToVacate) {
                slotEntityToVacate.setOccupied(false);
            }

            this.isParking = false;
            this.isParked = false;
            this.truckState = 'exited';
            this.intendedParkingSlotName = null;
            this.parkingTargetSlotInfo = null;
        },

        attemptSnapToParkingSlot: function (trajectoryEndpoint) {
            if (!trajectoryEndpoint || this.parkingSlots.length === 0 || this.truckState !== 'none') {
                return false;
            }

            var snapRadius = this.optimizationRadius || 150; // Use existing property for radius

            for (var i = 0; i < this.parkingSlots.length; i++) {
                var slot = this.parkingSlots[i];
                var slotEntity = slot.slotEntity;

                if (!slotEntity) continue;

                var validation = this._validateSlot(slot);
                if (!validation.status) continue;

                var frontPoint = slotEntity.getFrontPoint ? slotEntity.getFrontPoint() : null;
                if (!frontPoint) continue;

                // Condition 1: trajectory endpoint is within snapRadius of frontPoint
                var distToFrontPoint = ig.utils.distanceBetweenPoints(trajectoryEndpoint, frontPoint);

                if (distToFrontPoint < snapRadius) {
                    
                    // Prevent collision with the slot itself before snapping
                    if (this.shape && slotEntity && slotEntity.worldShape && this.satInstance) {
                        if (this.satInstance.simpleShapeIntersect(this.shape, slotEntity.worldShape)) {
                            continue; // Collision detected, try next slot
                        }
                    }

                    // We have a snap!
                    // Don't discard the user's path. Just add the front point.
                    this.trajectory.push(frontPoint);
                    
                    this.targetAngle = slot.orientationAngle;
                    
                    return true; // Snap occurred
                }
            }

            return false; // No snap
        },

        _validateSlot: function (slot) {
            var entity = slot.slotEntity;
            
            if (!entity) {
                return { status: false, reason: 'missing-entity' };
            }
            
            if (typeof entity.isCurrentlyOccupied === 'function' && entity.isCurrentlyOccupied()) {
                return { status: false, reason: 'occupied' };
            }
            
            if (slot.color !== this.color) {
                return { status: false, reason: 'color-mismatch' };
            }
            
            return { status: true, reason: 'valid' };
        },
        // #endregion
        // =================================================================================
        // #region COLLISION HANDLING
        // =================================================================================
        onCollision: function (collidedWith) {
            this.onCollisionInternal(collidedWith);
        },

        onCollisionInternal: function (collidedWith) {
            if (!this.collidesWith || !this.collidesWith.includes(collidedWith.type)) return;
            
            if ((collidedWith.type === LevelData.COLLISION_TYPES.BUILDING ||
                collidedWith.type === LevelData.COLLISION_TYPES.BOUNDARY) &&
                !this.isInBuildingCollisionResponse && !this.isParkingEngaged()) {
                
                this.trajectory = [];
                this.trajectoryComplete = true;
                // console.log('Collision with ' + collidedWith.name);
                this.startBuildingCollisionResponse();
                this.collisionCooldown = 0.5;
            }
        },
        
        onExitCollision: function (exitedCollision) {
            this.onExitCollisionInternal(exitedCollision);
        },

        onExitCollisionInternal: function (exitedCollision) {
            // This handles vacating a parking slot when the truck is no longer physically inside it
            if (exitedCollision.type === LevelData.COLLISION_TYPES.PARKING_SLOT &&
                this.parkingTargetSlotInfo &&
                this.parkingTargetSlotInfo.name === exitedCollision.name) {
                
                if (this.isParked || this.truckState === 'exiting') {
                    this.vacateSlot();
                    this.needsCollisionRestore = true;
                }
            }
        },

        updateCollisionResponseState: function () {
            if (this.collisionCooldown > 0) {
                this.collisionCooldown -= ig.system.tick;
            }
            if (this.isInBuildingCollisionResponse) {
                this.updateBuildingCollisionResponse();
            }
            
            if (this.needsCollisionRestore && !this.isParkingEngaged() && !this.isInBuildingCollisionResponse) {
                this.restoreCollisionMask();
            }
        },

        startBuildingCollisionResponse: function () {
            // Cancel any ongoing player input
            this.isClicked = false;
            this.isDragging = false;
            if (this.pathHead) { this.pathHead.kill(); this.pathHead = null; }
            if (ig.currentCtrl && ig.currentCtrl.currentVehicle === this) {
                ig.currentCtrl.currentVehicle = null;
            }

            this.isInBuildingCollisionResponse = true;
            this.buildingCollisionResponseProgress = 0;
            // Turn 180 degrees
            this.targetAngle = this.normalizeAngle(this.angle + Math.PI);

            // Temporarily disable collisions with buildings/boundaries to allow escape
            this._originalCollidesWith = this.collidesWith ? ig.copy(this.collidesWith) : [];
            this.setCollisionMaskForResponse(true);

            this.vel.x = 0;
            this.vel.y = 0;
        },

        updateBuildingCollisionResponse: function () {
            this.buildingCollisionResponseProgress += this.buildingCollisionResponseSpeed;
            
            if (this.buildingCollisionResponseProgress >= 1) {
                this.finishBuildingCollisionResponse();
                return;
            }

            var angleDiff = this.getShortestRotation(this.angle, this.targetAngle);
            // Use a sine ease-out for smooth rotation
            var easingFactor = Math.sin(this.buildingCollisionResponseProgress * Math.PI);
            this.angle += angleDiff * this.buildingCollisionResponseSpeed * 12 * easingFactor;
            this.angle = this.normalizeAngle(this.angle);
            this.angleChangedThisFrame = true;

            // Start moving away after turning for a bit
            if (this.buildingCollisionResponseProgress > 0.3) {
                var moveProgress = (this.buildingCollisionResponseProgress - 0.3) / 0.7;
                var moveSpeed = this.speed * 0.4 * moveProgress * moveProgress; // Ease-in speed
                var dir = this.getDirectionFromAngle();
                this.vel.x = dir.x * moveSpeed;
                this.vel.y = dir.y * moveSpeed;
            }
        },

        finishBuildingCollisionResponse: function () {
            this.angle = this.targetAngle;
            this.angleChangedThisFrame = true;
            this.isInBuildingCollisionResponse = false;
            this.buildingCollisionResponseProgress = 0;

            var dir = this.getDirectionFromAngle();
            this.vel.x = dir.x * this.speed * 0.6;
            this.vel.y = dir.y * this.speed * 0.6;

            // Restore original collision settings after a short delay
            this.delayedCall(0.3, function () {
                this.setCollisionMaskForResponse(false);
            }.bind(this));
        },
        
        restoreCollisionMask: function () {
            if (this._originalCollidesWith) {
                this.collidesWith = ig.copy(this._originalCollidesWith);
            }
            this.needsCollisionRestore = false;
        },
        isRespondingToCollision: function () {
            return this.isInBuildingCollisionResponse;
        },

        setCollisionMaskForResponse: function (isEnteringResponse) {
            if (isEnteringResponse) {
                if (this.collidesWith) {
                    // Remove building and boundary from collision checks
                    this.collidesWith = this.collidesWith.filter(function (type) {
                        return type !== (LevelData.COLLISION_TYPES.BUILDING || 1) &&
                               type !== (LevelData.COLLISION_TYPES.BOUNDARY || 2);
                    });
                }
            } else {
                // Flag to restore original collision settings
                this.needsCollisionRestore = true;
            }
        },

        checkOffScreen: function (isExit) {
            if (this.isInBuildingCollisionResponse || this.collisionCooldown > 0 || this.isParkingEngaged()) return;

            var isOffScreen = false;
            
            if (this.vertices && this.vertices.length > 0) {
                if (isExit) {
                    // For exiting, ALL vertices must be off-screen
                    isOffScreen = true;
                    for (var i = 0; i < this.vertices.length; i++) {
                        var vertex = this.vertices[i];
                        if (vertex.x >= 0 && vertex.x <= ig.system.width &&
                            vertex.y >= 0 && vertex.y <= ig.system.height) {
                            isOffScreen = false; // Found a vertex still on screen
                            break;
                        }
                    }
                } else {
                    // For boundary collision, ANY vertex must be off-screen
                    // Use attackShape vertices instead of all vertices
                    if (this.attackShape && this.attackShape.pointList && this.attackShape.pointList.length > 0) {
                        for (var i = 0; i < this.attackShape.pointList.length; i++) {
                            var vertex = this.attackShape.pointList[i];
                            if (vertex.x < 0 || vertex.x > ig.system.width ||
                                vertex.y < 0 || vertex.y > ig.system.height) {
                                isOffScreen = true;
                                break;
                            }
                        }
                    } else {
                        // Fallback to vertices if attackShape is not available
                        for (var i = 0; i < this.vertices.length; i++) {
                            var vertex = this.vertices[i];
                            if (vertex.x < 0 || vertex.x > ig.system.width ||
                                vertex.y < 0 || vertex.y > ig.system.height) {
                                isOffScreen = true;
                                break;
                            }
                        }
                    }
                }
            } else {
                // Fallback to AABB check if vertices are not available
                if (isExit) {
                    isOffScreen = (this.pos.x + this.size.x < 0 || this.pos.x > ig.system.width ||
                                   this.pos.y + this.size.y < 0 || this.pos.y > ig.system.height);
                } else {
                    isOffScreen = (this.pos.x < 0 || this.pos.x + this.size.x > ig.system.width ||
                                   this.pos.y < 0 || this.pos.y + this.size.y > ig.system.height);
                }
            }

            if (isOffScreen) {
                // console.log("Truck is off screen");
                if (isExit) {
                    // console.log("Exiting.");
                    this.onExitScreen();
                } else {
                    // console.log("Collide.");
                    // Treat going off-screen as a collision with the boundary
                    this.onCollision({ type: LevelData.COLLISION_TYPES.BOUNDARY, name: 'screen-boundary' });
                }
            }
        },
        // #endregion
        // =================================================================================
        // #region GAME STATE
        // =================================================================================
        onGameOver: function () {
            this.vel.x = 0;
            this.vel.y = 0;
            this.isSpawning = false;
            this.resetWaypointTimeout();
            this.clearTrajectory();
            this.smokeParticles = []; // Clear smoke particles
            this.clearProximityState(); // Clear proximity detection state
        },

        onExitScreen: function () {
            this.smokeParticles = []; // Clear smoke particles before killing
            this.clearProximityState(); // Clear proximity detection state
            this.kill();
            if (ig.currentCtrl && typeof ig.currentCtrl.addScore === 'function') {
                ig.currentCtrl.addScore(1);
            }
        },

        clearProximityState: function () {
            this.nearbyTrucks = [];
            this.exitProximityWarningState();
            this.proximityCheckTimer = 0;
            this.proximityHonkTimer = 0;
        }
        // #endregion
    });
});
